import {IconArrowRight} from '@/icons/mcp';
import {StepData} from './types';
import {
    ProcessCard,
    ProcessHeader,
    ProcessTitle,
    ProcessDetailButton,
    ProcessSteps,
    ProcessStep,
    StepIndicator,
    StepContent,
    StepTitle,
    StepDescription,
} from './styles';

export const ProcessStepsList = ({steps}: {steps: StepData[]}) => (
    <ProcessSteps>
        {steps.map((step, index) => (
            <ProcessStep key={index}>
                <StepIndicator />
                <StepContent>
                    <StepTitle>{step.title}</StepTitle>
                    <StepDescription>{step.description}</StepDescription>
                </StepContent>
            </ProcessStep>
        ))}
    </ProcessSteps>
);

interface ProcessCardProps {
    title: string;
    steps: StepData[];
    onClick?: () => void;
}

export const ProcessCardComponent = ({title, steps, onClick}: ProcessCardProps) => {
    return (
        <ProcessCard>
            <ProcessHeader>
                <ProcessTitle>{title}</ProcessTitle>
                <ProcessDetailButton type="text" size="small" onClick={onClick}>
                    查看详情<IconArrowRight />
                </ProcessDetailButton>
            </ProcessHeader>
            <ProcessStepsList steps={steps} />
        </ProcessCard>
    );
};
